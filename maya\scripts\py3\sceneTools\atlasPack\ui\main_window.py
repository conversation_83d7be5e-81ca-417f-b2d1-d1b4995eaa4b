from pyside_compat import *
# from shiboken6 import wrapInstance
import maya.OpenMayaUI as omui
from ..core.processor import AtlasProcessor
from .widgets.preview_widget import PreviewFrame
from ..utils.atlas_generator import TextureAtlasCreator


class MainWindow(QMainWindow):
	_instance = None

	@staticmethod
	def get_instance():
		if MainWindow._instance is None:
			MainWindow._instance = MainWindow(parent=get_maya_main_window())
		else:
			MainWindow._instance.raise_()
			MainWindow._instance.activateWindow()
		return MainWindow._instance

	def __init__(self, parent=None):
		if MainWindow._instance is not None:
			raise RuntimeError("MainWindow instance already exists. Use get_instance().")

		super(MainWindow, self).__init__(parent)
		MainWindow._instance = self

		# self.atlas_dimension = 1024  # Default dimension for the atlas
		self.output_directory = None
		self.filename = "texture_atlas"

		self.processor = AtlasProcessor()
		self.atlas_generator = TextureAtlasCreator(processor=self.processor)

		self.setWindowTitle("Atlas Pack Tool")
		self.setGeometry(100, 100, 400, 300)
		self.setObjectName("AtlasPackMainWindow")

		# Setup UI components
		self._setup_ui()
		self._connect_signals()

		# Restore window position
		self.settings = QSettings("AtlasPackTool", "MainWindow")
		self.restore_geometry()

		self.set_size_preview()

	def _setup_ui(self):
		self.central_widget = QWidget()
		self.setCentralWidget(self.central_widget)
		self.main_layout = QVBoxLayout(self.central_widget)

		self._setup_preview_section()
		self._setup_packing_section()
		self._setup_atlas_section()

	def _setup_preview_section(self):
		self.image_widget = QWidget()
		self.image_widget.setMinimumSize(300, 300)
		self.image_layout = QVBoxLayout(self.image_widget)
		self.image_layout.setContentsMargins(0, 0, 0, 0)  # Remove margins
		self.image_layout.setAlignment(Qt.AlignCenter)  # Center the content

		self.image_frame = PreviewFrame(processor=self.processor)

		# Set the widget reference in the processor for updates
		self.processor.preview_widget = self.image_frame  # Set the widget reference in the processor for updates
		self.image_layout.addWidget(self.image_frame)
		self.main_layout.addWidget(self.image_widget, 1)

	def _setup_packing_section(self):
		self.packing_group_box = QGroupBox("Packing Preview")
		self.packing_layout = QGridLayout(self.packing_group_box)

		self.pack_square_button = QPushButton("Pack Square")
		self.pack_square_button.setToolTip("Generates a preview by packing textures as squares.")
		self.pack_half_button = QPushButton("Pack Half")
		self.pack_half_button.setToolTip("Generates a preview by packing textures as horizontal half square.")

		self.packing_layout.addWidget(self.pack_square_button, 0, 0)
		self.packing_layout.addWidget(self.pack_half_button, 0, 1)

		self.main_layout.addWidget(self.packing_group_box)

	def _setup_atlas_section(self):
		self.output_group_box = QGroupBox("Atlas Generation")
		self.output_layout = QFormLayout(
			self.output_group_box)  # QFormLayout is convenient for "label: field" pairs
		self.output_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)  # Fields will expand

		# Directory Path
		self.path_layout = QHBoxLayout()
		self.output_dir_line_edit = QLineEdit()
		self.output_dir_line_edit.setPlaceholderText("e.g., D:/Projects/MyGame/Textures")
		self.output_dir_line_edit.setToolTip("Directory where the final texture atlas will be saved.")
		self.browse_button = QPushButton("Browse...")
		self.browse_button.setToolTip("Open a dialog to select the output directory.")
		self.path_layout.addWidget(self.output_dir_line_edit)
		self.path_layout.addWidget(self.browse_button)
		self.output_layout.addRow("Output Directory:", self.path_layout)

		# Filename
		self.output_filename_line_edit = QLineEdit("texture_atlas.png")  # Default filename
		self.output_filename_line_edit.setPlaceholderText("e.g., character_atlas.png")
		self.output_filename_line_edit.setToolTip("Name of the final texture atlas file (e.g., atlas.png, atlas.jpg).")
		self.output_layout.addRow("Filename:", self.output_filename_line_edit)

		self.generate_atlas_button = QPushButton("Generate Atlas")
		self.generate_atlas_button.setToolTip("Creates the final texture atlas image file using the settings above.")
		self.generate_atlas_button.setFixedHeight(30)  # Make the button more noticeable
		self.generate_atlas_button.setEnabled(False)  # Initially inactive

		self.restore_button = QPushButton("Restore")
		self.restore_button.setToolTip("Restore the previous state before atlas packing.")
		self.restore_button.setFixedHeight(30)

		# Add the atlas generation and restore buttons together
		self.output_action_layout = QHBoxLayout()
		self.output_action_layout.addWidget(self.generate_atlas_button)
		self.output_action_layout.addWidget(self.restore_button)
		self.output_layout.addRow(self.output_action_layout)  # Add as a row in QFormLayout

		self.main_layout.addWidget(self.output_group_box)

	def _connect_signals(self):
		"""Connect UI signals to their respective slots"""
		self.pack_square_button.clicked.connect(self._on_pack_clicked)
		self.pack_half_button.clicked.connect(lambda: self._on_pack_clicked(half=True))
		self.browse_button.clicked.connect(self._on_browse_clicked)
		self.generate_atlas_button.clicked.connect(self._on_atlas_clicked)
		self.restore_button.clicked.connect(self._on_restore_clicked)

		self.output_dir_line_edit.textChanged.connect(self._on_directory_changed)
		self.output_filename_line_edit.textChanged.connect(self._on_filename_changed)

	def _on_browse_clicked(self):
		"""Handle browse button click"""
		directory = QFileDialog.getExistingDirectory(
			self,
			"Select Output Directory",
			self.output_directory,
			QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
		)
		if directory:
			self.output_directory = directory
			self.output_dir_line_edit.setText(directory)

	def _on_directory_changed(self, text):
		"""Handle directory input change"""
		self.output_directory = text

	def _on_filename_changed(self, text):
		"""Handle filename input change"""
		self.filename = text

	def _update_ui_state(self):
		"""Updates the enabled/disabled state of buttons based on application state."""
		has_packed_data = bool(self.processor.texture_data)
		self.generate_atlas_button.setEnabled(has_packed_data)

	def closeEvent(self, event):
		self.save_geometry()
		MainWindow._instance = None
		super().closeEvent(event)  # changed

	def save_geometry(self):
		self.settings.setValue("geometry", self.saveGeometry())
		self.settings.setValue("output_directory", self.output_directory)
		self.settings.setValue("filename", self.filename)

	def restore_geometry(self):
		geometry = self.settings.value("geometry")
		if geometry:
			self.restoreGeometry(geometry)

		saved_directory = self.settings.value("output_directory")
		if saved_directory:
			self.output_directory = saved_directory
			self.output_dir_line_edit.setText(saved_directory)

		saved_filename = self.settings.value("filename")
		if saved_filename:
			self.filename = saved_filename
			self.output_filename_line_edit.setText(saved_filename)

	def set_size_preview(self):
		size = min(self.image_widget.width(), self.image_widget.height())
		size = max(size, 300)  # Not more than 300px
		self.image_frame.setFixedSize(size, size)

	def _on_pack_clicked(self, half=False):
		"""Handle Pack/Re-pack button click"""
		self.processor.success = False  # Reset the success flag before new processing
		self.processor.half_square = half  # Set the flag for half square
		self.processor.texture_proc(half)  # Use the method from AtlasProcessor
		self.set_size_preview()
		self.image_frame.render_to_buffer()
		self.image_frame.update()

		# print('>>>RELATIVE SIZE:', self.processor.relative_size)
		# print('>>>CANVAS DIMENSION:', self.processor.canvas_dimension)
		self._update_ui_state()

	def _on_atlas_clicked(self):
		"""Handle Atlas button click"""
		self.processor.success = False  # Reset the success flag before new processing
		self.image_frame.update()
		self.atlas_generator.set_output_path(f"{self.output_directory}/{self.filename}")
		self.processor.success = self.atlas_generator.write_atlas_to_file()  # Create the atlas and save it to a file
		if self.processor.success:
			atlas_faces = self.processor.mapping_proc()
			self.processor.assign_material_to_faces(self.filename, atlas_faces)

			self.image_frame.update()

	def _on_restore_clicked(self):
		"""Restore the previous state before atlas packing"""
		# self.processor.restore_previous_state()
		# self.image_frame.render_to_buffer()
		# self.image_frame.update()
		# self._update_ui_state()
		pass

	def resizeEvent(self, event):
		"""Support square form of image_widget when the window size changes"""
		super().resizeEvent(event)
		self.set_size_preview()


# Maya parent window
def get_maya_main_window():
	main_window_ptr = omui.MQtUtil.mainWindow()
	return wrapInstance(int(main_window_ptr), QWidget)
