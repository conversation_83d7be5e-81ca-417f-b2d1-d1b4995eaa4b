import os
import traceback
from .maya_utils import get_materials_from_mesh, get_texture_file_from_material_color, get_image_dimensions, \
	get_selected_meshes, get_materials_from_selection, create_proxy_mesh


def collect_texture_data(selected_objects):
	"""
	Accepts a list of mesh or material names.
	Returns a list of dictionaries with keys: material_name, texture_name, texture_path, width, height.
	"""
	texture_data = []
	for obj in selected_objects:

		materials = [obj]
		for mat in materials:
			texture_path = get_texture_file_from_material_color(mat)
			if texture_path:
				texture_name = os.path.basename(texture_path)
				width, height = get_image_dimensions(texture_path) or (None, None)
				texture_data.append({
					"material_name": mat,
					"texture_name": texture_name,
					"texture_path": texture_path,
					"width": width,
					"height": height
				})
	return texture_data


def main():
	"""
	Main function to execute the script.
	"""
	selected_objects = get_materials_from_selection()
	print("Selected objects:", selected_objects)
	if not selected_objects:
		print("No material or mesh with material selected.")
		return
	texture_data = collect_texture_data(selected_objects)
	for data in texture_data:
		print(data)
	create_proxy_mesh(texture_data)
