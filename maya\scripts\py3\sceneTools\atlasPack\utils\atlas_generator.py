# from PySide6.QtGui import QPixmap, QPainter, QImage, QImageReader, QColor
from pyside_compat import *
# from PySide6.QtCore import Qt, QCoreApplication
import os
import math


class TextureAtlasCreator:
	"""Класс для создания атласов текстур"""

	def __init__(self, processor=None):
		self.processor = processor
		self.output_path = None

	# self.images = []
	# self.atlas_width = 0
	# self.atlas_height = 0

	def set_output_path(self, output_path):
		"""
		Устанавливает путь для сохранения атласа.

		Args:
			output_path (str): Путь для сохранения атласа.
		"""
		self.output_path = output_path

	def write_atlas_to_file(self):
		"""
		Создание атласов текстур с различными суффиксами из texture_data.

		Returns:
			bool: True если хотя бы один атлас был создан успешно, False иначе
		"""
		if not self._validate_prerequisites():
			return False

		texture_types = ['_am', '_ao', '_nm', '_rmm', '_cm']
		successful_atlases = 0
		self.processor.atlas_textures = [None, None]

		for suffix in texture_types:
			if self._create_atlas_for_suffix(suffix):
				successful_atlases += 1

		return successful_atlases > 0

	def _validate_prerequisites(self):
		"""Проверяет наличие необходимых данных для создания атласа."""
		if not self.processor or not self.processor.texture_data:
			print("No texture data for atlas creation")
			return False

		if not self.output_path:
			print("No output path specified for atlas")
			return False

		return True

	def _create_atlas_for_suffix(self, suffix):
		"""
		Создает атлас для конкретного суффикса текстуры.

		Args:
			suffix (str): Суффикс типа текстуры (_am, _ao, etc.)

		Returns:
			bool: True если атлас создан успешно
		"""
		if not self._has_textures_with_suffix(suffix):
			print(f"Skipping atlas creation: no textures found with suffix {suffix}")
			return False

		output_path = f"{self.output_path}{suffix.upper()}.png"

		try:
			atlas_image = self._create_empty_atlas()
			painter = self._create_painter(atlas_image)

			processed_count = self._process_textures(painter, suffix)
			painter.end()

			if processed_count == 0:
				print(f"Unable to process any textures for {suffix}")
				return False

			# обрезать верхнюю половину атласа если self.half_square = True
			if self.processor.half_square:
				original_width = atlas_image.width()
				original_height = atlas_image.height()  # Это self.processor.canvas_dimension

				# Убедимся, что высота четная или обработаем нечетную корректно
				# Для QImage.copy(x, y, w, h), y - начальная строка, h - высота копируемой области
				# Нам нужна нижняя половина.

				crop_y = original_height // 2
				crop_height = original_height - crop_y  # Это гарантирует, что вся нижняя часть будет взята, даже при нечетной высоте

				if crop_height > 0 and original_width > 0:  # Проверка, что есть что копировать
					atlas_image = atlas_image.copy(0, crop_y, original_width, crop_height)
					print(
						f"Atlas for {suffix} cropped to bottom half: {atlas_image.width()}x{atlas_image.height()}")
				else:
					print(
						f"Unable to crop atlas for {suffix}: invalid crop dimensions ({original_width}x{crop_height} from {original_height})")
					del atlas_image
					return False

			success = atlas_image.save(output_path)
			if success:
				print(f"Atlas saved: {output_path} (processed textures: {processed_count})")
				if suffix == '_am':
					self.processor.atlas_textures[0] = output_path
				elif suffix == '_nm':
					self.processor.atlas_textures[1] = output_path
			else:
				print(f"Error saving atlas: {output_path}")

			return success

		except Exception as e:
			print(f"Error creating atlas {suffix}: {e}")
			return False

	def _has_textures_with_suffix(self, suffix):
		"""Проверяет наличие текстур с указанным суффиксом."""
		for item_data in self.processor.texture_data:
			texture_path = item_data.get('texture_path', '')
			if texture_path:
				current_path = texture_path.replace('_am', suffix)
				if os.path.exists(current_path):
					return True
		return False

	def _create_empty_atlas(self):
		"""Создает пустое изображение атласа."""
		width_canvas_size = height_canvas_size = self.processor.canvas_dimension
		# if self.processor.half_square:
		# 	height_canvas_size = int(height_canvas_size / 2)
		atlas_image = QImage(width_canvas_size, height_canvas_size, QImage.Format_ARGB32)
		atlas_image.fill(QColor(0, 0, 0, 0))  # Прозрачный фон
		return atlas_image

	def _create_painter(self, target_image):
		"""Создает настроенный QPainter для рисования на изображении."""
		painter = QPainter(target_image)
		painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
		return painter

	def _process_textures(self, painter, suffix):
		"""
		Обрабатывает и добавляет текстуры в атлас.

		Args:
			painter (QPainter): Настроенный painter
			suffix (str): Суффикс типа текстуры

		Returns:
			int: Количество успешно обработанных текстур
		"""
		processed_count = 0

		for item_data in self.processor.texture_data:
			if self._process_single_texture(painter, item_data, suffix):
				processed_count += 1

		return processed_count

	def _process_single_texture(self, painter, item_data, suffix):
		"""
		Обрабатывает одну текстуру и добавляет её в атлас.

		Args:
			painter (QPainter): Настроенный painter
			item_data (dict): Данные текстуры
			suffix (str): Суффикс типа текстуры

		Returns:
			bool: True если текстура успешно обработана
		"""
		texture_path = item_data.get('texture_path', '')
		if not texture_path:
			return False

		current_path = texture_path.replace('_am', suffix)

		if not os.path.exists(current_path):
			return False

		# Загружаем изображение
		source_image = self._load_texture_image(current_path)
		if source_image is None:
			return False

		# Вычисляем позицию вставки
		paste_position = self._calculate_paste_position(item_data)
		if paste_position is None:
			return False

		# Рисуем текстуру в атласе
		painter.drawImage(paste_position[0], paste_position[1], source_image)
		return True

	def _load_texture_image(self, texture_path):
		"""
		Загружает изображение текстуры с проверками.

		Args:
			texture_path (str): Путь к файлу текстуры

		Returns:
			QImage или None: Загруженное изображение или None при ошибке
		"""
		source_image = QImage()
		if not source_image.load(texture_path):
			print(f"Error loading texture: {texture_path}")
			return None

		# Обеспечиваем наличие альфа-канала
		if not source_image.hasAlphaChannel():
			source_image = source_image.convertToFormat(QImage.Format_ARGB32)

		return source_image

	def _calculate_paste_position(self, item_data):
		"""
		Вычисляет позицию для вставки текстуры в атлас.

		Args:
			item_data (dict): Данные элемента с позицией

		Returns:
			tuple или None: (x, y) координаты или None при ошибке
		"""
		position = item_data.get('position')
		if not self._validate_position_format(position):
			return None

		u_coords, v_coords = position[0], position[1]
		canvas_width_size = canvas_height_size = self.processor.canvas_dimension
		relative_size = self.processor.relative_size

		paste_x = int(u_coords[0] * canvas_width_size * relative_size)
		# if self.processor.half_square:
		# 	canvas_height_size = int(canvas_height_size / 2)

		paste_y = canvas_height_size - int(v_coords[1] * canvas_height_size * relative_size)

		return (paste_x, paste_y)

	def _validate_position_format(self, position):
		"""Проверяет корректность формата позиции."""
		if not position or len(position) != 2:
			return False

		u_coords, v_coords = position[0], position[1]
		return (isinstance(u_coords, (list, tuple)) and len(u_coords) == 2 and
		        isinstance(v_coords, (list, tuple)) and len(v_coords) == 2)
