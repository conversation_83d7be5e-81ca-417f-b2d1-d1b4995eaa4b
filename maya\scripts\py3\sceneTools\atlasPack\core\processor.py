import os
import traceback
from pyside_compat import *
# from PySide6 import QtWidgets, QtCore, QtGui
from .maya_utils import *


# from ..utils.atlas_generator import TextureAtlasCreator


class AtlasProcessor:
	def __init__(self):
		self.texture_data = []
		self.map_data = []
		self.map_size = (1, 1)
		self.max_dimension = 1024
		self.atlas_faces = []  # List to store faces of the atlas
		self.atlas_textures = [None, None]  # List to store textures of the atlas only .AM и .NM

		'''UI'''
		self.loaded_pixmaps = {}  # Cache for QPixmap objects
		self.canvas_dimension = 1024  # Default text for dimension
		self.range_factor = 1
		self.relative_size = 1.0  # Relative size for scaling
		self.buffer_dirty = True  # Flag to indicate buffer needs redraw
		self.success = False  # Flag to indicate if the process was successful
		self.half_square = False  # Flag for half square processing

		# Новые поля для отслеживания состояния
		self.loading_state = 'initial'
		self.current_loading_texture = None
		self.is_file_saved = False

		# Константы состояний загрузки
		self.LOADING_STATES = {
			'INITIAL': 'initial',
			'LOADING_TEXTURES': 'loading_textures',
			'PROCESSING_ATLAS': 'processing_atlas',
			'COMPLETED': 'completed'
		}

	def _collect_texture_data(self, selected_objects):
		for obj in selected_objects:
			materials = [obj]
			for mat in materials:
				texture_path = get_texture_file_from_material_color(mat)
				if texture_path:
					texture_name = os.path.basename(texture_path)
					width, height = get_image_dimensions(texture_path) or (None, None)
					self.texture_data.append({
						"material_name": mat,
						"texture_name": texture_name,
						"texture_path": texture_path,
						"width": width,
						"height": height
					})

	def texture_proc(self, half=False):
		self.texture_data = []
		self.map_data = []
		self.map_size = (1, 1)
		self.max_dimension = 1024

		selected_objects = get_materials_from_selection()
		if not selected_objects:
			# print("No material or mesh with material selected.")
			return
		self._collect_texture_data(selected_objects)
		actual_mesh, self.max_dimension = create_proxy_mesh(self.texture_data)
		self.map_size = layout_proxy_uvs(actual_mesh, half, self.max_dimension)
		polygon_2d_position = polygon_box(actual_mesh)
		for i, data in enumerate(self.texture_data):
			if i < len(polygon_2d_position):
				data["position"] = polygon_2d_position[i]
			self.map_data.append(data)

		self.canvas_precalculate()
		self._load_pixmaps()
		self.loading_state = self.LOADING_STATES['COMPLETED']
		self.buffer_dirty = True  # Mark buffer as dirty for redraw

	def mapping_proc(self):
		# self.canvas_dimension содержит разрешение атласа, например 1024, 2048 и т.д.
		self.atlas_faces.clear()  # Clear previous atlas faces

		for i, data in enumerate(self.texture_data):
			data["material_name"] = data.get("material_name", f"material_{i}")

			position = data.get('position')
			u_coords, v_coords = position[0], position[1]

			pos_u0 = int(u_coords[0] * self.canvas_dimension * self.relative_size)
			pos_u1 = int(u_coords[1] * self.canvas_dimension * self.relative_size)
			pos_v0 = int(v_coords[0] * self.canvas_dimension * self.relative_size)
			pos_v1 = int(v_coords[1] * self.canvas_dimension * self.relative_size)

			normalized_u0 = pos_u0 / self.canvas_dimension
			normalized_u1 = pos_u1 / self.canvas_dimension
			normalized_v0 = pos_v0 / self.canvas_dimension
			normalized_v1 = pos_v1 / self.canvas_dimension

			# print('>>>NORMALIZED_U0:', normalized_u0)
			# print('>>>NORMALIZED_U1:', normalized_u1)
			# print('>>>NORMALIZED_V0:', normalized_v0)
			# print('>>>NORMALIZED_V1:', normalized_v1)

			normalized_coords = {
				'u_min': normalized_u0,
				'u_max': normalized_u1,
				'v_min': normalized_v0,
				'v_max': normalized_v1
			}

			sg_name, face_list = get_polygons_from_material(data['material_name'])

			# if not face_list:
			# 	continue
			if face_list:
				# print(">>>SG NAME:", sg_name, "FACE LIST:", len(face_list))
				normalize_uv_shell_position(face_list, normalized_coords, 'map1')
				self.atlas_faces.append(face_list)
			else:
				continue
		return self.atlas_faces

	def assign_material_to_faces(self, mat_name, faces=None):
		if faces:
			shading_group = create_atlas_material(mat_name, self.atlas_textures)
			# print("SHADING GROUP:", shading_group)

			if shading_group:
				# Get shading group assuming it was created as mat_name + "SG"
				# shading_group = mat_name + "SG"
				# Assign the shading group to each list of face names
				for face_list in faces:
					cmds.sets(face_list, edit=True, forceElement=shading_group)
			# print("Assigned material '{}' to {} polygon groups.".format(shading_group, len(face_list)))

	def canvas_precalculate(self):
		presets = [1024, 2048, 4096, 8192]
		self.range_factor = max(self.map_size[0], self.map_size[1])
		self.canvas_dimension = self.max_dimension
		if self.range_factor > 1:
			try:
				current_index = presets.index(self.max_dimension)
				if current_index < len(presets) - 1:
					self.canvas_dimension = presets[current_index + 1]
			except ValueError:
				traceback.print_exc()
				pass
		# self.relative_size = self.canvas_dimension / self.max_dimension
		self.relative_size = self.max_dimension / self.canvas_dimension

	def _load_pixmaps(self):
		self.loaded_pixmaps.clear()
		for i, tex_info in enumerate(self.texture_data):
			path = tex_info.get('texture_path')
			if path and os.path.exists(path):  # Check if path exists
				pixmap = QPixmap(path)
				if not pixmap.isNull():
					self.loaded_pixmaps[i] = pixmap
		# print(f"Loaded pixmap from {path}")
		# else:
		# print(f"Warning: Could not load pixmap from {path}")

	# elif path:
	# print(f"Warning: Texture path does not exist: {path}")

	def update_loading_state(self, state, texture_name=None):
		"""Обновляет состояние загрузки и принудительно обновляет виджет"""
		self.loading_state = state
		self.current_loading_texture = texture_name
		self.buffer_dirty = True

		# Если есть ссылка на виджет, обновляем его
		if hasattr(self, 'preview_widget') and self.preview_widget:
			self.preview_widget.update()
